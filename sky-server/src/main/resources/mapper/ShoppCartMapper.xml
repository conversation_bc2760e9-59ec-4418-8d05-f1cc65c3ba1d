<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.ShoppingCartMapper">

    <update id="updateNumberById">
        update shopping_cart set number = #{number} where id = #{id}
    </update>


    <select id="list" resultType="com.sky.entity.ShoppingCart">
         select * from shopping_cart
         <where>
             <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="dishId != null">
                and dish_id = #{dishId},
            </if>
            <if test="setmealId != null">
                and setmeal_id = #{setmealId
            </if>
         </where>
    </select>

    <insert id="insert" >
        insert into shopping_cart (name, user_id, dish_id, setmeal_id, dish_flavor, number, amount, image, create_time) values (
        #{name}, #{userId}, #{dishId}, #{setmealId}, #{dishFlavor}, #{number}, #{amount}, #{image}, #{createTime})
    </insert>
</mapper>
