package com.sky.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis配置类
 * 配置Redis序列化器，支持LocalDateTime等Java 8时间类型的序列化
 * 解决Redis存储对象时的序列化问题
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@Configuration
@Slf4j
public class RedisConfiguration {

    /**
     * 配置RedisTemplate
     * 设置合适的序列化器，支持LocalDateTime等复杂类型
     *
     * @param connectionFactory Redis连接工厂
     * @return RedisTemplate 配置好的Redis模板
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        log.info("开始配置Redis序列化器，支持LocalDateTime");

        // 为了命名一致性，使用 redisTemplate 作为变量名
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);

        // 使用GenericJackson2JsonRedisSerializer，更简洁且支持泛型
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();

        // 字符串序列化器
        StringRedisSerializer stringSerializer = new StringRedisSerializer();

        // 设置key和value的序列化器
        redisTemplate.setKeySerializer(stringSerializer);           // key使用String序列化
        redisTemplate.setValueSerializer(jsonSerializer);           // value使用JSON序列化
        redisTemplate.setHashKeySerializer(stringSerializer);       // hash key使用String序列化
        redisTemplate.setHashValueSerializer(jsonSerializer);       // hash value使用JSON序列化

        // 初始化RedisTemplate
        redisTemplate.afterPropertiesSet();

        log.info("Redis序列化器配置完成，已支持LocalDateTime序列化");
        return redisTemplate;
    }
}